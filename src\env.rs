use once_cell::sync::Lazy;

pub struct Environment {
    pub discord_token: String,
    pub discord_guild_id: u64,
    pub trengo_token: String,
    pub trengo_custom_channel_token: String,
    pub trengo_custom_channel_id: String,
    pub trengo_webhook_secret: String,
    pub rocket_port: u16,
}

impl Environment {
    pub fn new() -> Self {
        Self {
            discord_token: std::env::var("DISCORD_TOKEN").expect("DISCORD_TOKEN is not set"),
            discord_guild_id: std::env::var("DISCORD_GUILD_ID")
                .expect("DISCORD_CLIENT_ID is not set")
                .parse::<u64>()
                .unwrap(),
            trengo_token: std::env::var("TRENGO_TOKEN").expect("TRENGO_TOKEN is not set"),
            trengo_custom_channel_token: std::env::var("TRENGO_CUSTOM_CHANNEL_TOKEN")
                .expect("TRENGO_CUSTOM_CHANNEL_TOKEN is not set"),
            trengo_custom_channel_id: std::env::var("TRENGO_CUSTOM_CHANNEL_ID")
                .expect("TRENGO_CUSTOM_CHANNEL_ID is not set"),
            trengo_webhook_secret: std::env::var("TRENGO_WEBHOOK_SECRET")
                .expect("TRENGO_WEBHOOK_URL is not set"),
            rocket_port: std::env::var("ROCKET_PORT")
                .expect("ROCKET_PORT is not set")
                .parse::<u16>()
                .expect("ROCKET_PORT is not unsigned 16-bit integer"),
        }
    }
}

pub static ENV: Lazy<Environment> = Lazy::new(Environment::new);
