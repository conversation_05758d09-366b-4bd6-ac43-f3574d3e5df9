use anyhow::Context;
use anyhow::Result;
use anyhow::anyhow;
use lru::LruCache;
use serenity::all::Channel;
use serenity::all::ChannelId;
use serenity::all::ChannelType;
use serenity::all::CreateChannel;
use serenity::all::GuildChannel;
use serenity::all::GuildId;
use serenity::all::Http;
use serenity::all::PermissionOverwrite;
use serenity::all::PermissionOverwriteType;
use serenity::all::UserId;
use std::fmt::Debug;
use std::num::NonZeroUsize;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::debug;

use crate::permissions;
use crate::tickets::models::Mailbox;
use crate::tickets::models::Ticket;
use crate::tickets::storage::Storage;

#[derive(Debug)]
pub struct TicketsService {
    discord_http: Arc<Http>,
    storage: Arc<Storage>,
    entries: RwLock<LruCache<UserId, ChannelId>>,
    mailbox: RwLock<Option<Mailbox>>,
    guild_id: Arc<GuildId>,
}

impl TicketsService {
    /// creates a new tickets service.
    ///
    /// upon creation the service will attempt to find the mailbox channel and set it.
    /// if the mailbox channel is not found, the service will not set it.
    ///
    /// # Arguments
    ///
    /// * `http` - the discord http client.
    /// * `guild_id` - the guild id of the tickets service.
    /// * `lru_capacity` - the capacity of the lru cache for tickets.
    ///
    /// # Errors
    ///
    /// `anyhow::Error` - if the service fails to find the mailbox channel due to an error.
    ///
    pub async fn new(http: Arc<Http>, guild_id: GuildId, lru_capacity: usize) -> Result<Self> {
        let ticket_service = Self {
            discord_http: Arc::clone(&http),
            storage: Arc::new(Storage::new(Arc::clone(&http), lru_capacity)),
            entries: RwLock::new(LruCache::new(
                NonZeroUsize::new(lru_capacity)
                    .expect("failed to create lru cache due zero capacity value"),
            )),
            mailbox: RwLock::new(None),
            guild_id: Arc::new(guild_id),
        };

        let guild = http
            .get_guild(guild_id)
            .await
            .context(format!("failed to fetch guild; guild_id={}", guild_id))?;

        let guild_channels = guild.channels(&http).await.context(format!(
            "failed to fetch channels of guild; guild_id={}",
            guild_id
        ))?;

        let mailbox_channel = guild_channels
            .values()
            .filter(|channel| channel.topic.is_some())
            .find(|channel| channel.topic.as_ref().unwrap().contains("category_id"));

        if let Some(mailbox_channel) = mailbox_channel {
            debug!("found mailbox channel; channel_id={}", mailbox_channel.id);

            let category_id = match mailbox_channel.parent_id {
                Some(parent_id) => Ok(parent_id),
                None => Err(anyhow!("failed to fetch parent id for mailbox channel")),
            }?;

            ticket_service
                .set_mailbox(Mailbox::new(
                    mailbox_channel.guild_id,
                    category_id,
                    mailbox_channel.id,
                ))
                .await?;
        } else {
            debug!("failed to find mailbox channel; guild_id={}", guild_id);
        }

        Ok(ticket_service)
    }

    /// sets the mailbox channel for the tickets service.
    ///
    /// # Arguments
    ///
    /// * `mailbox` - the mailbox to set.
    ///
    /// # Errors
    ///
    /// `anyhow::Error` - if the service fails to set the mailbox due to an error.
    ///
    pub async fn set_mailbox(&self, mailbox: Mailbox) -> Result<()> {
        self.storage
            .write(mailbox.channel_id, mailbox.clone())
            .await
            .context(format!(
                "failed to set mailbox; channel_id={}",
                mailbox.channel_id
            ))?;

        *self.mailbox.write().await = Some(mailbox.clone());

        debug!(
            "set mailbox; channel_id={} mailbox={}",
            mailbox.channel_id, mailbox
        );

        Ok(())
    }

    /// fetches the ticket and the ticket channel for the given user id.
    ///
    /// # Arguments
    ///
    /// * `id` - the user id of the ticket.
    ///
    /// # Errors
    ///
    /// `anyhow::Error` - if the service fails to fetch the ticket due to an error.
    ///
    /// # Returns
    ///  
    /// `Option<(Ticket, GuildChannel)>` - the ticket and the ticket channel for the given user id.
    ///
    pub async fn get_ticket(&self, id: &UserId) -> Result<Option<(Ticket, GuildChannel)>> {
        match self.entries.read().await.peek(&id) {
            Some(channel_id) => {
                let record = self.storage.read(channel_id).await?;

                if record.is_none() {
                    return Ok(None);
                }

                let channel = self
                    .discord_http
                    .get_channel(*channel_id)
                    .await
                    .context(format!(
                        "failed to fetch channel for record; guild_id={} channel_id={}",
                        self.guild_id, id
                    ))?;

                let guild_channel = match channel {
                    Channel::Guild(channel) => Ok(channel),
                    _ => Err(anyhow!(
                        "failed to fetch channel for record; guild_id={} channel_id={}",
                        self.guild_id,
                        id
                    )),
                }?;

                debug!(
                    "fetched ticket; user_id={} channel_id={}",
                    id, guild_channel.id
                );

                Ok(Some((record.unwrap(), guild_channel)))
            }
            None => {
                let guild = self
                    .discord_http
                    .get_guild(*self.guild_id)
                    .await
                    .context(format!("failed to fetch guild; guild_id={}", self.guild_id))?;

                let channels = guild.channels(&self.discord_http).await.context(format!(
                    "failed to fetch channels of guild; guild_id={}",
                    self.guild_id
                ))?;

                let stringified_id = id.to_string();

                let found_channel = channels
                    .values()
                    .filter(|channel| channel.topic.is_some())
                    .find(|channel| channel.topic.as_ref().unwrap().contains(&stringified_id));

                if let Some(found_channel) = found_channel {
                    let record = self.storage.read(&found_channel.id).await?;

                    if record.is_none() {
                        return Ok(None);
                    }

                    self.entries.write().await.put(*id, found_channel.id);

                    let channel = self
                        .discord_http
                        .get_channel(found_channel.id)
                        .await
                        .context(format!(
                            "failed to fetch channel for record; guild_id={} channel_id={}",
                            self.guild_id, id
                        ))?;

                    let guild_channel = match channel {
                        Channel::Guild(channel) => Ok(channel),
                        _ => Err(anyhow!(
                            "failed to fetch channel for record; guild_id={} channel_id={}",
                            self.guild_id,
                            id
                        )),
                    }?;

                    debug!(
                        "fetched ticket by search; user_id={} channel_id={}",
                        id, guild_channel.id
                    );

                    return Ok(Some((record.unwrap(), guild_channel)));
                }

                debug!("ticket not found; user_id={}", id);

                Ok(None)
            }
        }
    }

    /// fetches the ticket and the ticket channel if it is created by calling the function for the given user id.
    ///
    /// # Arguments
    ///
    /// * `id` - the user id of the ticket.
    ///
    /// # Errors
    ///
    /// `anyhow::Error` - if the service fails to fetch the ticket due to an error.
    ///
    /// # Returns
    ///
    /// `(Ticket, Option<GuildChannel>)` - the ticket and if a channel is created in this call, the ticket channel will be returned as well.
    ///
    pub async fn upsert_ticket(&self, id: &UserId) -> Result<(Ticket, Option<GuildChannel>)> {
        let user = self.discord_http.get_user(*id).await.context(format!(
            "failed to fetch user for ticket; guild_id={} user_id={}",
            self.guild_id, id,
        ))?;

        match self.entries.read().await.peek(&id) {
            Some(channel_id) => {
                let record = Ticket::new(user.id, *channel_id, user.id.into(), user.name.into());

                self.storage.write(*channel_id, record.clone()).await?;

                debug!("updated ticket; user_id={}", id);

                Ok((record, None))
            }
            None => {
                let mailbox = self.mailbox.read().await.clone().context(format!(
                    "failed to fetch mailbox; guild_id={}",
                    self.guild_id
                ))?;

                let guild = self
                    .discord_http
                    .get_guild(*self.guild_id)
                    .await
                    .context(format!("failed to fetch guild; guild_id={}", self.guild_id))?;

                let guild_channels = guild.channels(&self.discord_http).await.context(format!(
                    "failed to fetch channels of guild; guild_id={}",
                    self.guild_id
                ))?;

                let user_id = id.to_string();

                let channel = guild_channels
                    .values()
                    .find(|channel| channel.name == user_id);

                match channel {
                    Some(channel) => {
                        let record =
                            Ticket::new(user.id, channel.id, user.id.into(), user.name.into());

                        self.storage.write(channel.id, record.clone()).await?;

                        debug!("updated ticket; user_id={} channel_id={}", id, channel.id);

                        Ok((record, None))
                    }
                    None => {
                        let everyone_permissions = PermissionOverwrite {
                            allow: permissions::ticket_channel::EVERYONE_ALLOW,
                            deny: permissions::ticket_channel::EVERYONE_DENY,
                            kind: PermissionOverwriteType::Role(self.guild_id.everyone_role()),
                        };

                        let user_permissions = PermissionOverwrite {
                            allow: permissions::ticket_channel::USER_ALLOW,
                            deny: permissions::ticket_channel::USER_DENY,
                            kind: PermissionOverwriteType::Member(UserId::from(id)),
                        };

                        let guild_channel = self
                            .guild_id
                            .create_channel(
                                &self.discord_http,
                                CreateChannel::new(id.to_string())
                                    .kind(ChannelType::Text)
                                    .category(mailbox.category_id)
                                    .permissions(vec![everyone_permissions, user_permissions]),
                            )
                            .await
                            .context(format!(
                                "failed to create channel for record; guild_id={}",
                                self.guild_id
                            ))?;

                        let user = self.discord_http.get_user(*id).await.context(format!(
                            "failed to fetch user for ticket; guild_id={} user_id={}",
                            self.guild_id, id,
                        ))?;

                        let record = Ticket::new(
                            user.id,
                            guild_channel.id,
                            user.id.into(),
                            user.name.into(),
                        );

                        self.storage.write(guild_channel.id, record.clone()).await?;

                        self.entries.write().await.put(user.id, guild_channel.id);

                        debug!(
                            "created ticket; user_id={} channel_id={}",
                            id, guild_channel.id
                        );

                        Ok((record, Some(guild_channel)))
                    }
                }
            }
        }
    }
}
