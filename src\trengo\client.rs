use once_cell::sync::Lazy;
use reqwest::Client;
use reqwest::header::{ACCEPT, AUTHORIZATION, CONTENT_TYPE, HeaderMap, HeaderValue};

use crate::ENV;

pub const BASE_URL: &str = "https://app.trengo.com/api/v2";

pub static CLIENT: Lazy<Client> = Lazy::new(|| {
    let token = &ENV.trengo_token;

    let mut headers = HeaderMap::new();

    headers.insert(ACCEPT, HeaderValue::from_static("application/json"));
    headers.insert(CONTENT_TYPE, HeaderValue::from_static("application/json"));

    headers.insert(
        AUTHORIZATION,
        HeaderValue::from_str(&format!("Bearer {}", token))
            .expect("invalid authorization header value"),
    );

    Client::builder()
        .default_headers(headers)
        .build()
        .expect("failed to create Trengo HTTP client")
});
