use serenity::all::{
    Channel, ChannelType, CommandInteraction, CommandOptionType, Context, CreateCommand,
    CreateCommandOption, CreateInteractionResponse, CreateInteractionResponseMessage,
    CreateMessage, GetMessages, InteractionResponseFlags, PermissionOverwrite,
    PermissionOverwriteType, Permissions, ResolvedOption, ResolvedValue,
};
use tracing::{error, info};

use crate::{
    components::{
        MAILBOX_CHANNEL_CONFIGURED_MESSAGE, UNEXPECTED_ERROR_MESSAGE, create_ticket_embed,
        ticket_create_button,
    },
    state::SerenityState,
    tickets::models::Mailbox,
};

pub async fn run<'a>(
    ctx: &'a Context,
    _interaction: &CommandInteraction,
    options: &'a [ResolvedOption<'a>],
) -> CreateInteractionResponse {
    let mailbox_channel = {
        let channel_id = match {
            if let Some(ResolvedOption {
                value: ResolvedValue::Channel(channel),
                ..
            }) = options.first()
            {
                Some(channel.id)
            } else {
                None
            }
        } {
            Some(channel_id) => channel_id,
            None => {
                return CreateInteractionResponse::Message(
                    CreateInteractionResponseMessage::new()
                        .content("please provide a channel to create tickets from")
                        .flags(InteractionResponseFlags::EPHEMERAL),
                );
            }
        };

        match ctx.http.get_channel(channel_id).await {
            Ok(channel) => match channel {
                Channel::Guild(channel) => channel,
                _ => {
                    error!("failed to fetch channel; channel_id={}", channel_id);

                    return CreateInteractionResponse::Message(
                        CreateInteractionResponseMessage::new()
                            .content(UNEXPECTED_ERROR_MESSAGE)
                            .flags(InteractionResponseFlags::EPHEMERAL),
                    );
                }
            },
            Err(error) => {
                error!(
                    "failed to fetch channel; channel_id={} error={:#?}",
                    channel_id, error
                );

                return CreateInteractionResponse::Message(
                    CreateInteractionResponseMessage::new()
                        .content(UNEXPECTED_ERROR_MESSAGE)
                        .flags(InteractionResponseFlags::EPHEMERAL),
                );
            }
        }
    };

    let mailbox_parent_id = match mailbox_channel.parent_id {
        Some(id) => id,
        None => {
            error!(
                "failed to fetch parent id for channel; channel_id={}",
                mailbox_channel.id
            );

            return CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(UNEXPECTED_ERROR_MESSAGE)
                    .flags(InteractionResponseFlags::EPHEMERAL),
            );
        }
    };

    let data = ctx.data.read().await;

    let state = match data.get::<SerenityState>() {
        Some(state) => state,
        None => {
            error!(
                "failed to get SerenityState from the data; channel_id={}",
                mailbox_channel.id
            );

            return CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(UNEXPECTED_ERROR_MESSAGE)
                    .flags(InteractionResponseFlags::EPHEMERAL),
            );
        }
    };

    if let Err(error) = state
        .tickets
        .set_mailbox(Mailbox::new(
            mailbox_channel.guild_id,
            mailbox_parent_id,
            mailbox_channel.id,
        ))
        .await
    {
        error!("failed to set mailbox; error={:#?}", error);

        return CreateInteractionResponse::Message(
            CreateInteractionResponseMessage::new()
                .content(UNEXPECTED_ERROR_MESSAGE)
                .flags(InteractionResponseFlags::EPHEMERAL),
        );
    }

    info!(
        "configured mailbox channel; guild_id={} channel_id={}",
        mailbox_channel.guild_id, mailbox_channel.id
    );

    if let Err(error) = mailbox_channel
        .create_permission(
            &ctx.http,
            PermissionOverwrite {
                allow: Permissions::empty(),
                deny: Permissions::SEND_MESSAGES,
                kind: PermissionOverwriteType::Role(mailbox_channel.guild_id.everyone_role()),
            },
        )
        .await
    {
        error!(
            "failed to set mailbox channel permissions; channel_id={} error={:#?}",
            mailbox_channel.id, error
        );

        return CreateInteractionResponse::Message(
            CreateInteractionResponseMessage::new()
                .content(UNEXPECTED_ERROR_MESSAGE)
                .flags(InteractionResponseFlags::EPHEMERAL),
        );
    }

    match mailbox_channel
        .messages(&ctx.http, GetMessages::default())
        .await
    {
        Ok(messages) => {
            if let Err(error) = mailbox_channel.delete_messages(&ctx.http, messages).await {
                error!(
                    "failed to delete messages from mailbox channel; channel_id={} error={:#?}",
                    mailbox_channel.id, error
                );
            }

            info!(
                "deleted messages from mailbox channel; channel_id={}",
                mailbox_channel.id
            );

            if let Err(error) = mailbox_channel
                .send_message(
                    &ctx.http,
                    CreateMessage::new()
                        .add_embed(create_ticket_embed())
                        .button(ticket_create_button()),
                )
                .await
            {
                error!(
                    "failed to send ticket creator embed to mailbox channel; channel_id={} error={:#?}",
                    mailbox_channel.id, error
                );

                return CreateInteractionResponse::Message(
                    CreateInteractionResponseMessage::new()
                        .content(UNEXPECTED_ERROR_MESSAGE)
                        .flags(InteractionResponseFlags::EPHEMERAL),
                );
            }

            info!(
                "configured mailbox channel; channel_id={}",
                mailbox_channel.id
            );
        }
        Err(error) => {
            error!(
                "failed to delete messages from mailbox channel; channel_id={} error={:#?}",
                mailbox_channel.id, error
            );

            return CreateInteractionResponse::Message(
                CreateInteractionResponseMessage::new()
                    .content(UNEXPECTED_ERROR_MESSAGE)
                    .flags(InteractionResponseFlags::EPHEMERAL),
            );
        }
    }

    CreateInteractionResponse::Message(
        CreateInteractionResponseMessage::new()
            .content(MAILBOX_CHANNEL_CONFIGURED_MESSAGE)
            .flags(InteractionResponseFlags::EPHEMERAL),
    )
}

pub fn register() -> CreateCommand {
    CreateCommand::new("set-mailbox")
        .description("configure's the mailbox channel")
        .add_option(
            CreateCommandOption::new(
                CommandOptionType::Channel,
                "mailbox",
                "the channel to create tickets from",
            )
            .channel_types(vec![ChannelType::Text])
            .required(true),
        )
        .default_member_permissions(Permissions::ADMINISTRATOR)
}
