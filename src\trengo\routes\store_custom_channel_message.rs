use anyhow::{Context, Result, bail};
use serde::Serialize;

use crate::trengo::{
    BASE_URL, CLIENT,
    models::{ContactId, ContractName},
};

#[derive(Debug, Serialize)]
pub struct StoreCustomChannelMessageBody {
    pub channel: String,
    pub contact_identifier: ContactId,
    pub contact_name: ContractName,
    pub content: String,
}

/// sends a message to a custom channel in trengo.
pub async fn store_custom_channel_message(body: StoreCustomChannelMessageBody) -> Result<()> {
    let target = format!("/custom_channel_messages");

    let response = CLIENT
        .post(format!("{BASE_URL}{target}"))
        .json(&serde_json::json!({
          "channel": body.channel,
          "contact": {
            "identifier": body.contact_identifier,
            "name": body.contact_name,
          },
          "body": {
            "text": body.content
          },
        }))
        .send()
        .await
        .context("failed to send request to trengo; path={target}")?;

    if response.status() != 200 {
        bail!(
            "failed to store message in trengo; response={:#?}",
            response
        );
    }

    Ok(())
}
