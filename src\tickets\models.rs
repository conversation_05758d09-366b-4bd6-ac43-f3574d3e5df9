use std::fmt::Display;

use serde::{Deserialize, Ser<PERSON><PERSON>};
use serenity::{
    all::GuildId,
    model::id::{ChannelId, UserId},
};

use crate::trengo::models::{ContactId, ContractName};

#[derive(Debu<PERSON>, <PERSON><PERSON>, De<PERSON>ult, Serial<PERSON>, Deserialize)]
pub struct Mailbox {
    pub guild_id: GuildId,
    pub category_id: ChannelId,
    pub channel_id: ChannelId,
}

impl Mailbox {
    pub fn new(guild_id: GuildId, category_id: ChannelId, channel_id: ChannelId) -> Self {
        Self {
            guild_id,
            category_id,
            channel_id,
        }
    }
}

impl Display for Mailbox {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "mailbox|guild_id={}|category_id={}|channel_id={}",
            self.guild_id, self.category_id, self.channel_id
        )
    }
}

#[derive(Debug, <PERSON><PERSON>, De<PERSON>ult, Serialize, Deserialize)]
pub struct Ticket {
    pub user_id: UserId,
    pub channel_id: ChannelId,
    pub contact_id: ContactId,
    pub contact_name: ContractName,
}

impl Ticket {
    pub fn new(
        user_id: UserId,
        channel_id: ChannelId,
        contact_id: ContactId,
        contact_name: ContractName,
    ) -> Self {
        Self {
            user_id,
            channel_id,
            contact_id,
            contact_name,
        }
    }
}

impl Display for Ticket {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "ticket|user_id={}|channel_id={}|contact_id={}|contact_name={}",
            self.user_id, self.channel_id, self.contact_id, self.contact_name
        )
    }
}
