# Ignore a blackhole and the folder for development
node_modules/
.vs/
.idea/
*.iml

# Yarn files
.yarn/install-state.gz
.yarn/build-state.yml

# Environment variables
.DS_Store

dist/

# Ignore heapsnapshot and log files
*.heapsnapshot
*.log

# Ignore npm lockfiles file
package-lock.json

# Environment variables
.env*
.env.local
.env.development.local
.env.test.local
.env.production.local

# Ignore the database file
*.db

# Added by cargo

/target
