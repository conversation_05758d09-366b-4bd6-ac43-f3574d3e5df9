use serenity::all::CreateInteractionResponse;
use serenity::all::CreateInteractionResponseMessage;
use serenity::all::GuildId;
use serenity::all::Interaction;
use serenity::all::InteractionResponseFlags;
use serenity::all::Ready;
use serenity::async_trait;
use serenity::prelude::*;
use tracing::error;
use tracing::info;
use tracing::warn;

use crate::ENV;
use crate::commands;

pub struct Handler;

#[async_trait]
impl EventHandler for <PERSON>ler {
    async fn interaction_create(&self, ctx: Context, interaction: Interaction) {
        if let Interaction::Command(interaction) = &interaction {
            let options = &interaction.data.options();

            let response = match interaction.data.name.as_str() {
                "set-mailbox" => {
                    info!(
                        "recv command execution request; interaction_id={} command_name={}",
                        interaction.id, interaction.data.name
                    );

                    let response = commands::set_mailbox::run(&ctx, interaction, options).await;

                    info!(
                        "executed command; interaction_id={} command_name={}",
                        interaction.id, interaction.data.name
                    );

                    response
                }
                name => {
                    warn!(
                        "recv command execution request for unknown command; interaction_id={} command_name={}",
                        interaction.id, name
                    );

                    CreateInteractionResponse::Message(
                        CreateInteractionResponseMessage::new()
                            .content(format!("unknown command `{}`", name))
                            .flags(InteractionResponseFlags::EPHEMERAL),
                    )
                }
            };

            if let Err(error) = interaction.create_response(&ctx.http, response).await {
                error!(
                    "failed to create response for interaction; interaction_id={} command_name={} error={:#?}",
                    interaction.id, interaction.data.name, error
                );
            }

            info!(
                "response for interaction was sent; interaction_id={} command_name={}",
                interaction.id, interaction.data.name
            );
        }
    }

    async fn ready(&self, ctx: Context, _ready: Ready) {
        let guild_id = GuildId::new(ENV.discord_guild_id);

        let commands = vec![commands::set_mailbox::register()];

        for command in &commands {
            info!(
                "registered command in guild; guild_id={} command={:#?}",
                guild_id, command
            );
        }

        guild_id.set_commands(&ctx.http, commands).await.ok();

        info!("registered commands in guild; guild_id={}", guild_id);
    }
}
