pub mod base62 {
    const CHARS: &[u8] = b"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const BASE: u64 = CHARS.len() as u64;

    /// Encodes a u64 number into a base62 string.
    pub fn encode(id: u64) -> String {
        if id == 0 {
            return "0".to_string();
        }

        let mut n = id;
        let mut result = String::new();

        while n > 0 {
            let rem = (n % BASE) as usize;
            result.insert(0, CHARS[rem] as char);
            n /= BASE;
        }

        result
    }

    /// Decodes a base62 string back into a u64 number.
    /// Returns `None` if the input contains invalid characters.
    pub fn decode(id: &str) -> Option<u64> {
        if id.is_empty() {
            return None;
        }

        let mut result: u64 = 0;

        for c in id.chars() {
            let index = CHARS.iter().position(|&ch| ch as char == c)?;

            result = result.checked_mul(BASE)?.checked_add(index as u64)?;
        }

        Some(result)
    }

    #[cfg(test)]
    mod tests {
        use super::*;

        #[test]
        fn test_encode() {
            assert_eq!(encode(222504065953693696), "gr4oR9Io0g");
        }

        #[test]
        fn test_decode() {
            assert_eq!(decode("gr4oR9Io0g"), Some(222504065953693696));
        }
    }
}
