use std::sync::Arc;

use dotenv::dotenv;
use rocket::routes;
use serenity::{all::GuildId, prelude::*};
use ttx_integration::{
    ENV, TicketsService, interactions, routes,
    state::{RocketState, SerenityState},
};

#[tokio::main]
async fn main() {
    dotenv().ok();

    tracing_subscriber::fmt::init();

    let token = &ENV.discord_token;

    let intents = GatewayIntents::GUILDS
        | GatewayIntents::GUILD_MESSAGES
        | GatewayIntents::GUILD_MEMBERS
        | GatewayIntents::MESSAGE_CONTENT;

    let mut discord_client = Client::builder(token, intents)
        .event_handler(interactions::on_message::Handler)
        .event_handler(interactions::on_ticket_create_button_press::Handler)
        .event_handler(interactions::on_command_interaction::Handler)
        .await
        .expect("failed to create discord client");

    let tickets_service = Arc::new(
        TicketsService::new(
            Arc::clone(&discord_client.http),
            GuildId::from(ENV.discord_guild_id),
            100,
        )
        .await
        .expect("failed to create tickets service"),
    );

    // NOTE: to allow for the data lock to be released a scope must be created
    {
        let mut data = discord_client.data.write().await;

        data.insert::<SerenityState>(SerenityState {
            tickets: Arc::clone(&tickets_service),
        });
    }

    let rocket_server = rocket::build()
        .configure(rocket::Config {
            port: ENV.rocket_port,
            ..Default::default()
        })
        .manage(RocketState {
            discord_http: Arc::clone(&discord_client.http),
            tickets: Arc::clone(&tickets_service),
        })
        .mount("/", routes![routes::webhook::handler]);

    let (client_connection_result, server_lunch_result) =
        tokio::join!(discord_client.start(), rocket_server.launch());

    if let Err(why) = client_connection_result {
        eprintln!("error while connecting to discord: {:?}", why);
    }

    if let Err(why) = server_lunch_result {
        eprintln!("error while launching rocket server: {:?}", why);
    }
}
