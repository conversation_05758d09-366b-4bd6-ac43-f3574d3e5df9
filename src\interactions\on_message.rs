use serenity::all::Channel;
use serenity::async_trait;
use serenity::model::channel::{ChannelType, Message};
use serenity::prelude::*;
use tracing::{debug, error, info};

use crate::state::SerenityState;
use crate::{
    ENV, regex,
    trengo::{self, StoreCustomChannelMessageBody},
};

pub struct Handler;

#[async_trait]
impl EventHandler for Handler {
    async fn message(&self, ctx: Context, msg: Message) {
        if msg.author.bot {
            debug!("recv message from bot; user_id={}", msg.author.id);

            return;
        }

        let channel = match msg.channel(&ctx.http).await {
            Ok(channel) => match channel {
                Channel::Guild(channel) => channel,
                _ => {
                    error!(
                        "failed to fetch channel as guild channel; channel_id={}",
                        msg.channel_id
                    );

                    return;
                }
            },
            Err(error) => {
                error!(
                    "failed to fetch channel; channel_id={} error={:#?}",
                    msg.channel_id, error
                );

                return;
            }
        };

        if channel.kind != ChannelType::Text || !regex::SNOWFLAKE.is_match(&channel.name) {
            debug!(
                "recv message from non-ticket channel; channel_id={}",
                msg.channel_id,
            );

            return;
        }

        let ticket = {
            let ctx_data = ctx.data.read().await;

            let state = match ctx_data.get::<SerenityState>() {
                Some(state) => state,
                None => {
                    error!(
                        "failed to get SerenityState from the data; channel_id={}",
                        msg.channel_id
                    );

                    return;
                }
            };

            match state.tickets.get_ticket(&msg.author.id).await {
                Ok(result) => match result {
                    Some((ticket, _)) => ticket,
                    None => {
                        error!(
                            "failed to fetch ticket; user_id={} channel_id={}",
                            msg.author.id, msg.channel_id
                        );

                        return;
                    }
                },
                Err(error) => {
                    error!(
                        "failed to fetch ticket; user_id={} channel_id={} error={:#?}",
                        msg.author.id, msg.channel_id, error
                    );

                    return;
                }
            }
        };

        info!(
            "received request for transport of message to trengo; user_id={} channel_id={}",
            msg.author.id, msg.channel_id
        );

        if !msg.content.is_empty() {
            let body = StoreCustomChannelMessageBody {
                channel: ENV.trengo_custom_channel_token.clone(),
                contact_identifier: ticket.contact_id.clone(),
                contact_name: ticket.contact_name.clone(),
                content: msg.content,
            };

            if let Err(error) = trengo::store_custom_channel_message(body).await {
                error!(
                    "failed to store message in trengo; user_id={} channel_id={} error={:#?}",
                    msg.author.id, msg.channel_id, error
                );
            }

            info!(
                "stored message in trengo; user_id={} channel_id={} msg_id={}",
                msg.author.id, msg.channel_id, msg.id
            );
        }

        for (i, attachment) in msg.attachments.iter().enumerate() {
            let body = StoreCustomChannelMessageBody {
                channel: ENV.trengo_custom_channel_token.clone(),
                contact_identifier: ticket.contact_id.clone(),
                contact_name: ticket.contact_name.clone(),
                content: attachment.url.clone(),
            };

            if let Err(error) = trengo::store_custom_channel_message(body).await {
                error!(
                    "failed to store attachment[{}/{}] in trengo; user_id={} channel_id={} msg_id={} error={:#?}",
                    i + 1,
                    msg.attachments.len(),
                    msg.author.id,
                    msg.channel_id,
                    msg.id,
                    error
                );
            }
        }

        info!(
            "stored attachments({}) in trengo; user_id={} channel_id={} msg_id={}",
            msg.attachments.len(),
            msg.author.id,
            msg.channel_id,
            msg.id
        );
    }
}
