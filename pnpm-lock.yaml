lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    devDependencies:
      smee-client:
        specifier: ^4.1.2
        version: 4.1.2

packages:

  eventsource-parser@3.0.2:
    resolution: {integrity: sha512-6RxOBZ/cYgd8usLwsEl+EC09Au/9BcmCKYF2/xbml6DNczf7nv0MQb+7BA2F+li6//I+28VNlQR37XfQtcAJuA==}
    engines: {node: '>=18.0.0'}

  eventsource@4.0.0:
    resolution: {integrity: sha512-fvIkb9qZzdMxgZrEQDyll+9oJsyaVvY92I2Re+qK0qEJ+w5s0X3dtz+M0VAPOjP1gtU3iqWyjQ0G3nvd5CLZ2g==}
    engines: {node: '>=20.0.0'}

  smee-client@4.1.2:
    resolution: {integrity: sha512-WNRrE4KDlmcqlnAu2p5XrkBKL0tgnaK5xtSbcNTP3Cnyn0mOrCvol/BIYj3XjEINutJCm54gVIw4pJvdakFDYQ==}
    engines: {node: ^20.18 || >= 22}
    hasBin: true

  undici@7.9.0:
    resolution: {integrity: sha512-e696y354tf5cFZPXsF26Yg+5M63+5H3oE6Vtkh2oqbvsE2Oe7s2nIbcQh5lmG7Lp/eS29vJtTpw9+p6PX0qNSg==}
    engines: {node: '>=20.18.1'}

  validator@13.15.0:
    resolution: {integrity: sha512-36B2ryl4+oL5QxZ3AzD0t5SsMNGvTtQHpjgFO5tbNxfXbMFkY822ktCDe1MnlqV3301QQI9SLHDNJokDI+Z9pA==}
    engines: {node: '>= 0.10'}

snapshots:

  eventsource-parser@3.0.2: {}

  eventsource@4.0.0:
    dependencies:
      eventsource-parser: 3.0.2

  smee-client@4.1.2:
    dependencies:
      eventsource: 4.0.0
      undici: 7.9.0
      validator: 13.15.0

  undici@7.9.0: {}

  validator@13.15.0: {}
