use anyhow::{Context, Result, bail};
use serde::Deserialize;

use crate::trengo::{BASE_URL, CLIENT};

#[derive(Debug)]
pub struct Message {
    pub content: String,
    pub attachment_urls: Vec<String>,
}

#[derive(Debug, Deserialize)]
struct MessageJson {
    pub message: String,
    pub attachments: Vec<AttachmentJson>,
}

#[derive(Debug, Deserialize)]
struct AttachmentJson {
    pub full_url: String,
}

/// fetches a message from a ticket in trengo.
pub async fn fetch_message(ticket_id: &str, message_id: &str) -> Result<Message> {
    let target = format!("/tickets/{ticket_id}/messages/{message_id}");

    let response = CLIENT
        .get(format!("{BASE_URL}{target}"))
        .send()
        .await
        .context(format!("failed to send request to trengo; path={target}"))?;

    if response.status() != 200 {
        bail!(
            "failed to fetch message from trengo; response={:#?}",
            response
        );
    }

    let json = response.json::<MessageJson>().await.context(format!(
        "failed to parse message response as json from trengo; path={target}"
    ))?;

    Ok(Message {
        content: json.message,
        attachment_urls: json
            .attachments
            .into_iter()
            .map(|attachment| attachment.full_url)
            .collect(),
    })
}
